const axios = require('axios');

async function fixMessageTemplates() {
  try {
    console.log('正在调用消息模板修复 API...');
    
    const response = await axios.post('http://localhost:3001/dev-tools/fix-message-templates', {}, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('API 调用成功！');
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (response.data.success) {
      const { totalTemplates, addedTemplates, orderFinishedExists } = response.data.data;
      console.log('\n修复结果:');
      console.log(`- 总模板数: ${totalTemplates}`);
      console.log(`- 新增模板数: ${addedTemplates}`);
      console.log(`- ORDER_FINISHED 模板: ${orderFinishedExists ? '✅ 存在' : '❌ 不存在'}`);
      
      if (orderFinishedExists) {
        console.log('\n🎉 ORDER_FINISHED 模板修复成功！');
      } else {
        console.log('\n⚠️ ORDER_FINISHED 模板仍然缺失，请检查日志');
      }
    }
    
  } catch (error) {
    console.error('修复失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 请确保服务器正在运行:');
      console.log('   npm run dev');
      console.log('   或');
      console.log('   npm start');
    } else if (error.response) {
      console.log('服务器响应:', error.response.status, error.response.data);
    }
  }
}

// 运行修复
fixMessageTemplates();
