const { Sequelize, DataTypes } = require('sequelize');

// 数据库配置 - 根据实际环境调整
const sequelize = new Sequelize('pet_manager', 'remote_user', 'Aa@123456', {
  host: '***********',
  dialect: 'mysql',
  logging: false,
});

// 定义消息模板模型
const MessageTemplate = sequelize.define('MessageTemplate', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  code: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
  },
  type: {
    type: DataTypes.STRING(20),
    allowNull: false,
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  variables: {
    type: DataTypes.TEXT,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
  },
}, {
  tableName: 'message_templates',
  timestamps: true,
});

// 消息模板初始数据
const messageTemplates = [
  // 员工端消息模板
  {
    code: 'ORDER_NEW',
    type: 'order',
    title: '新订单通知',
    content: '您有新的订单待接单，订单号：{{orderNo}}，客户：{{customerName}}',
    variables: JSON.stringify({ orderNo: '订单号', customerName: '客户姓名' }),
    isActive: true,
  },
  {
    code: 'ORDER_ACCEPTED',
    type: 'order',
    title: '订单已接单',
    content: '订单{{orderNo}}已成功接单，请按时提供服务',
    variables: JSON.stringify({ orderNo: '订单号' }),
    isActive: true,
  },
  {
    code: 'ORDER_COMPLETED',
    type: 'order',
    title: '服务完成通知',
    content: '订单{{orderNo}}服务已完成，感谢您的使用',
    variables: JSON.stringify({ orderNo: '订单号' }),
    isActive: true,
  },
  // 用户端消息模板
  {
    code: 'ORDER_CONFIRMED',
    type: 'order',
    title: '订单确认成功',
    content: '您的订单{{orderNo}}已确认，金额{{totalAmount}}元，服务时间：{{serviceTime}}，请耐心等待员工接单',
    variables: JSON.stringify({
      orderNo: '订单号',
      totalAmount: '订单金额',
      serviceTime: '服务时间',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_EMPLOYEE_ASSIGNED',
    type: 'order',
    title: '员工已接单',
    content: '您的订单{{orderNo}}已被员工{{employeeName}}接单，联系电话：{{employeePhone}}，请保持电话畅通',
    variables: JSON.stringify({
      orderNo: '订单号',
      employeeName: '员工姓名',
      employeePhone: '员工电话',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_DISPATCHED',
    type: 'order',
    title: '员工已出发',
    content: '服务员工{{employeeName}}已出发前往您的服务地址，订单号：{{orderNo}}，请做好准备',
    variables: JSON.stringify({
      orderNo: '订单号',
      employeeName: '员工姓名',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_STARTED',
    type: 'order',
    title: '服务已开始',
    content: '您的{{serviceName}}服务已开始，订单号：{{orderNo}}，请配合员工完成服务',
    variables: JSON.stringify({
      orderNo: '订单号',
      serviceName: '服务名称',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_FINISHED',
    type: 'order',
    title: '服务已完成',
    content: '您的{{serviceName}}服务已完成，订单号：{{orderNo}}，感谢您的使用，欢迎对本次服务进行评价',
    variables: JSON.stringify({
      orderNo: '订单号',
      serviceName: '服务名称',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_CANCELLED',
    type: 'order',
    title: '订单已取消',
    content: '您的订单{{orderNo}}已取消{{reason}}，如有疑问请联系客服',
    variables: JSON.stringify({
      orderNo: '订单号',
      reason: '取消原因',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_REFUNDED',
    type: 'order',
    title: '订单已退款',
    content: '您的订单{{orderNo}}已成功退款，退款金额{{refundAmount}}元，资金将在1-3个工作日内到账',
    variables: JSON.stringify({
      orderNo: '订单号',
      refundAmount: '退款金额',
    }),
    isActive: true,
  },
  {
    code: 'ORDER_TIME_CHANGED',
    type: 'order',
    title: '服务时间已修改',
    content: '您的订单{{orderNo}}服务时间已由{{modifier}}修改为{{newServiceTime}}，请注意时间安排',
    variables: JSON.stringify({
      orderNo: '订单号',
      modifier: '修改人',
      newServiceTime: '新服务时间',
    }),
    isActive: true,
  },
  // 系统消息模板
  {
    code: 'SYSTEM_MAINTENANCE',
    type: 'system',
    title: '系统维护通知',
    content: '系统将于{{maintenanceTime}}进行维护，预计耗时{{duration}}',
    variables: JSON.stringify({
      maintenanceTime: '维护时间',
      duration: '维护时长',
    }),
    isActive: true,
  },
  {
    code: 'PLATFORM_POLICY',
    type: 'platform',
    title: '平台政策更新',
    content: '平台服务政策已更新，请及时查看最新内容',
    variables: JSON.stringify({}),
    isActive: true,
  },
];

async function checkAndFixMessageTemplates() {
  try {
    console.log('连接数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 检查当前数据库中的消息模板
    console.log('\n检查当前消息模板...');
    const existingTemplates = await MessageTemplate.findAll();
    console.log(`当前数据库中有 ${existingTemplates.length} 个消息模板`);

    // 检查 ORDER_FINISHED 模板是否存在
    const orderFinishedTemplate = existingTemplates.find(t => t.code === 'ORDER_FINISHED');
    if (orderFinishedTemplate) {
      console.log('✅ ORDER_FINISHED 模板已存在');
      console.log('模板内容:', orderFinishedTemplate.toJSON());
    } else {
      console.log('❌ ORDER_FINISHED 模板不存在');
    }

    // 显示所有现有模板
    console.log('\n现有模板列表:');
    existingTemplates.forEach(template => {
      console.log(`- ${template.code}: ${template.title} (${template.isActive ? '启用' : '禁用'})`);
    });

    // 检查缺失的模板并添加
    console.log('\n检查缺失的模板...');
    let addedCount = 0;
    
    for (const templateData of messageTemplates) {
      const existing = existingTemplates.find(t => t.code === templateData.code);
      if (!existing) {
        console.log(`添加缺失的模板: ${templateData.code}`);
        await MessageTemplate.create(templateData);
        addedCount++;
      }
    }

    if (addedCount > 0) {
      console.log(`\n✅ 成功添加 ${addedCount} 个缺失的消息模板`);
    } else {
      console.log('\n✅ 所有消息模板都已存在');
    }

    // 再次检查 ORDER_FINISHED 模板
    const finalCheck = await MessageTemplate.findOne({ where: { code: 'ORDER_FINISHED' } });
    if (finalCheck) {
      console.log('\n✅ ORDER_FINISHED 模板现在可用');
    } else {
      console.log('\n❌ ORDER_FINISHED 模板仍然不存在');
    }

  } catch (error) {
    console.error('错误:', error);
  } finally {
    await sequelize.close();
    console.log('\n数据库连接已关闭');
  }
}

// 运行检查
checkAndFixMessageTemplates();
