import {
  InjectClient,
  Logger,
  Provide,
  Scope,
  ScopeEnum,
} from '@midwayjs/core';
import {
  Area,
  Dictionary,
  Feature,
  Permission,
  Role,
  User,
  UserAttributes,
  MessageTemplate,
  Employee,
} from '../entity';
import {
  initData_areas,
  initData_dict,
  initData_feature,
  initData_permission,
  initData_messageTemplates,
} from '../common/InitData';
import { generateUniqueCode } from '../common/Utils';
import { CachingFactory, MidwayCache } from '@midwayjs/cache-manager';
import { Op } from 'sequelize';

@Provide()
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class SystemService {
  @InjectClient(CachingFactory, 'dictionary')
  cache_dictionary: MidwayCache;

  @Logger()
  logger;
  async checkArea() {
    const areaCount = await Area.count();
    if (!areaCount) {
      this.logger.info('初始化地区数据');
      await Area.bulkCreate(initData_areas);
    }
  }

  async checkFeature() {
    const count_feature = await Feature.count();
    if (!count_feature) {
      this.logger.info('初始化功能数据');
      await Feature.bulkCreate(initData_feature);
    }
  }

  async checkPermission() {
    const count_permission = await Permission.count();
    if (!count_permission) {
      this.logger.info('初始化权限数据');
      await Permission.bulkCreate(initData_permission);
    }
  }

  async checkRole() {
    const role = await Role.findOne({
      where: {
        name: '管理员',
      },
    });
    if (!role) {
      this.logger.info('初始化角色数据');
      const admin = await Role.create({
        name: '管理员',
        description: '内置身份',
      });
      await admin.$set(
        'permissions',
        await Permission.findAll({ where: { name: '管理' } })
      );
    }
  }

  async checkUser() {
    const user = await User.findOne({
      where: {
        username: 'admin',
      },
    });
    if (!user) {
      this.logger.info('初始化管理员账号');
      const adminUser: Omit<UserAttributes, 'id'> = {
        username: 'admin',
        password: 'BC@admin123',
        nickname: '系统管理员',
      };
      await User.destroy({ where: {} });
      const user = await User.create(adminUser);
      user.$set('roles', await Role.findOne({ where: { name: '管理员' } }));
    }
  }

  async checkDict() {
    const count_dict = await Dictionary.count();
    if (!count_dict) {
      this.logger.info('初始化字典');
      await Dictionary.bulkCreate(initData_dict);
    }
  }

  async checkMessageTemplates() {
    const count_templates = await MessageTemplate.count();
    if (!count_templates) {
      this.logger.info('初始化消息模板');
      await MessageTemplate.bulkCreate(initData_messageTemplates);
    } else {
      // 检查是否有缺失的模板
      this.logger.info('检查缺失的消息模板');
      const existingTemplates = await MessageTemplate.findAll({
        attributes: ['code'],
      });
      const existingCodes = existingTemplates.map(t => t.code);

      const missingTemplates = initData_messageTemplates.filter(
        template => !existingCodes.includes(template.code)
      );

      if (missingTemplates.length > 0) {
        this.logger.info(`发现 ${missingTemplates.length} 个缺失的消息模板，正在添加...`);
        for (const template of missingTemplates) {
          this.logger.info(`添加消息模板: ${template.code}`);
        }
        await MessageTemplate.bulkCreate(missingTemplates);
        this.logger.info('缺失的消息模板添加完成');
      } else {
        this.logger.info('所有消息模板都已存在');
      }
    }
  }

  /**
   * 强制重新检查并添加缺失的消息模板
   * 用于修复消息模板缺失的问题
   */
  async forceCheckMessageTemplates() {
    this.logger.info('强制检查消息模板...');

    // 获取现有模板
    const existingTemplates = await MessageTemplate.findAll({
      attributes: ['code', 'title', 'isActive'],
    });
    const existingCodes = existingTemplates.map(t => t.code);

    this.logger.info(`当前数据库中有 ${existingTemplates.length} 个消息模板`);

    // 检查 ORDER_FINISHED 模板
    const orderFinishedExists = existingCodes.includes('ORDER_FINISHED');
    this.logger.info(`ORDER_FINISHED 模板${orderFinishedExists ? '已存在' : '不存在'}`);

    // 查找缺失的模板
    const missingTemplates = initData_messageTemplates.filter(
      template => !existingCodes.includes(template.code)
    );

    if (missingTemplates.length > 0) {
      this.logger.info(`发现 ${missingTemplates.length} 个缺失的消息模板:`);
      missingTemplates.forEach(template => {
        this.logger.info(`- ${template.code}: ${template.title}`);
      });

      // 添加缺失的模板
      await MessageTemplate.bulkCreate(missingTemplates);
      this.logger.info('✅ 缺失的消息模板已添加');
    } else {
      this.logger.info('✅ 所有消息模板都已存在');
    }

    // 再次检查 ORDER_FINISHED 模板
    const finalCheck = await MessageTemplate.findOne({
      where: { code: 'ORDER_FINISHED' },
    });

    if (finalCheck) {
      this.logger.info(`✅ ORDER_FINISHED 模板现在可用: ${finalCheck.title} (${finalCheck.isActive ? '启用' : '禁用'})`);
    } else {
      this.logger.error('❌ ORDER_FINISHED 模板仍然不存在');
    }

    return {
      totalTemplates: existingTemplates.length + missingTemplates.length,
      addedTemplates: missingTemplates.length,
      orderFinishedExists: !!finalCheck,
    };
  }

  async checkEmployeePromotionCodes() {
    try {
      // 检查是否有员工没有推广码，为其生成
      const employeesWithoutCode = await Employee.findAll({
        where: {
          [Op.or]: [
            { promotionCode: { [Op.is]: null } },
            { promotionCode: '' },
          ],
        },
      });

      if (employeesWithoutCode.length > 0) {
        this.logger.info(`为 ${employeesWithoutCode.length} 个员工生成推广码`);

        for (const employee of employeesWithoutCode) {
          let promotionCode;
          let isUnique = false;

          // 确保生成的推广码是唯一的
          while (!isUnique) {
            promotionCode = generateUniqueCode();
            const existing = await Employee.findOne({
              where: { promotionCode },
            });
            if (!existing) {
              isUnique = true;
            }
          }

          await employee.update({ promotionCode });
          this.logger.info(
            `员工 ${employee.name} 生成推广码: ${promotionCode}`
          );
        }
      }
    } catch (error) {
      this.logger.error('检查员工推广码时出错:', error.message);
      // 如果是表不存在或字段不存在的错误，跳过初始化
      if (
        error.message.includes("doesn't exist") ||
        error.message.includes('Unknown column')
      ) {
        this.logger.warn('数据库表结构可能需要更新，跳过员工推广码初始化');
        return;
      }
      throw error;
    }
  }

  async checkAndInitSystem() {
    this.checkDict();
    await this.checkArea();
    await this.checkFeature();
    await this.checkPermission();
    await this.checkRole();
    await this.checkUser();
    await this.checkMessageTemplates(); // 启用消息模板初始化
    await this.checkEmployeePromotionCodes(); // 初始化员工推广码
  }

  async initCache() {
    console.log('初始化缓存');
    const dictionary = await Dictionary.findAll();
    console.log('缓存字典, 数量: ', dictionary.length);
    for (const dic of dictionary) {
      await this.cache_dictionary.set(dic.code, dic);
    }
    console.log('缓存字典完成');
    console.log('初始化缓存完成');
  }
}
