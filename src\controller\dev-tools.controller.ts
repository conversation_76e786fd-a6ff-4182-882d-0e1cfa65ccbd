import { Controller, Inject, Post } from '@midwayjs/core';
import { SystemService } from '../service/system.service';

@Controller('/dev-tools')
export class DevToolsController {
  @Inject()
  systemService: SystemService;

  /**
   * 强制检查并修复消息模板
   * 用于修复 ORDER_FINISHED 等缺失的消息模板
   */
  @Post('/fix-message-templates', { summary: '修复消息模板' })
  async fixMessageTemplates() {
    const result = await this.systemService.forceCheckMessageTemplates();
    return {
      success: true,
      message: '消息模板检查完成',
      data: result,
    };
  }
}
