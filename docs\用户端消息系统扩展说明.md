# 用户端消息系统扩展说明

## 概述

基于现有的员工端消息系统，我们扩展了消息功能，为用户端在关键业务步骤中也提供相应的消息通知。这样可以让用户及时了解订单状态变化，提升用户体验。

## 扩展内容

### 1. 新增消息模板常量

在 `src/common/Constant.ts` 中新增了用户端消息模板代码：

```typescript
// 用户端消息模板
ORDER_CONFIRMED: 'ORDER_CONFIRMED',           // 订单确认（付款成功）
ORDER_EMPLOYEE_ASSIGNED: 'ORDER_EMPLOYEE_ASSIGNED', // 员工已接单
ORDER_DISPATCHED: 'ORDER_DISPATCHED',        // 员工已出发
ORDER_STARTED: 'ORDER_STARTED',              // 服务已开始
ORDER_FINISHED: 'ORDER_FINISHED',            // 服务已完成
ORDER_CANCELLED: 'ORDER_CANCELLED',          // 订单已取消
ORDER_REFUNDED: 'ORDER_REFUNDED',            // 订单已退款
ORDER_TIME_CHANGED: 'ORDER_TIME_CHANGED',    // 服务时间已修改
```

### 2. 扩展消息助手类

在 `src/utils/message.helper.ts` 中新增了用户端消息发送方法：

- `sendOrderConfirmedNotification()` - 发送订单确认通知
- `sendOrderEmployeeAssignedNotification()` - 发送员工接单通知
- `sendOrderDispatchedNotification()` - 发送员工出发通知
- `sendOrderStartedNotification()` - 发送服务开始通知
- `sendOrderFinishedNotification()` - 发送服务完成通知
- `sendOrderCancelledNotification()` - 发送订单取消通知
- `sendOrderRefundedNotification()` - 发送订单退款通知
- `sendOrderTimeChangedNotification()` - 发送服务时间修改通知

### 3. 新增消息模板数据

在 `src/common/InitData.ts` 中新增了用户端消息模板的初始化数据：

| 模板代码 | 消息标题 | 消息内容 |
|---------|---------|---------|
| ORDER_CONFIRMED | 订单确认成功 | 您的订单{{orderNo}}已确认，金额{{totalAmount}}元，服务时间：{{serviceTime}}，请耐心等待员工接单 |
| ORDER_EMPLOYEE_ASSIGNED | 员工已接单 | 您的订单{{orderNo}}已被员工{{employeeName}}接单，联系电话：{{employeePhone}}，请保持电话畅通 |
| ORDER_DISPATCHED | 员工已出发 | 服务员工{{employeeName}}已出发前往您的服务地址，订单号：{{orderNo}}，请做好准备 |
| ORDER_STARTED | 服务已开始 | 您的{{serviceName}}服务已开始，订单号：{{orderNo}}，请配合员工完成服务 |
| ORDER_FINISHED | 服务已完成 | 您的{{serviceName}}服务已完成，订单号：{{orderNo}}，感谢您的使用，欢迎对本次服务进行评价 |
| ORDER_CANCELLED | 订单已取消 | 您的订单{{orderNo}}已取消{{reason}}，如有疑问请联系客服 |
| ORDER_REFUNDED | 订单已退款 | 您的订单{{orderNo}}已成功退款，退款金额{{refundAmount}}元，资金将在1-3个工作日内到账 |
| ORDER_TIME_CHANGED | 服务时间已修改 | 您的订单{{orderNo}}服务时间已由{{modifier}}修改为{{newServiceTime}}，请注意时间安排 |

### 4. 订单服务中的消息发送

在 `src/service/order.service.ts` 中的关键业务节点添加了用户端消息发送：

#### 4.1 订单支付成功 (`payOrder`)
- 发送订单确认消息，包含订单号、金额、服务时间

#### 4.2 员工接单 (`acceptOrder`)
- 发送员工接单消息，包含订单号、员工姓名、员工电话

#### 4.3 员工出发 (`order_dispatch`)
- 发送员工出发消息，包含订单号、员工姓名

#### 4.4 服务开始 (`order_start`)
- 发送服务开始消息，包含订单号、服务名称

#### 4.5 服务完成 (`order_complete`)
- 发送服务完成消息，包含订单号、服务名称

#### 4.6 订单取消 (`cancelOrder`)
- 发送订单取消消息，包含订单号、取消原因

#### 4.7 订单退款 (`refundOrder`)
- 发送订单退款消息，包含订单号、退款金额

#### 4.8 服务时间修改 (`updateServiceTime`)
- 发送服务时间修改消息，包含订单号、新服务时间、修改人

## 消息流程对比

### 员工端消息流程
1. 用户付款 → 员工收到新订单通知
2. 员工接单 → 员工收到接单成功通知
3. 订单完成 → 员工收到完成通知

### 用户端消息流程
1. 用户付款 → 用户收到订单确认通知
2. 员工接单 → 用户收到员工接单通知
3. 员工出发 → 用户收到员工出发通知
4. 服务开始 → 用户收到服务开始通知
5. 服务完成 → 用户收到服务完成通知
6. 订单取消 → 用户收到订单取消通知
7. 订单退款 → 用户收到订单退款通知
8. 时间修改 → 用户收到时间修改通知

## 使用方式

用户端使用与员工端相同的消息接口，但通过JWT Token中的用户类型进行权限控制：

- **获取消息列表**: `GET /api/message/list/{userId}`
- **获取消息详情**: `GET /api/message/detail/{messageId}`
- **标记消息已读**: `POST /api/message/mark-read/{messageId}`
- **标记所有消息已读**: `POST /api/message/mark-all-read/{userId}`
- **获取未读消息数量**: `GET /api/message/unread-count/{userId}`
- **删除消息**: `DELETE /api/message/delete/{messageId}`

### 权限控制

系统通过JWT Token中的`userType`字段区分用户类型：
- `customer` - 客户端用户
- `employee` - 员工端用户
- `admin` - 管理端用户

用户只能查看和操作自己的消息，系统会验证JWT中的`userId`与请求参数中的`userId`是否一致。

## 注意事项

1. 所有用户端消息都是自动发送的，无需手动触发
2. 消息发送失败不会影响业务流程的正常执行
3. 消息模板支持变量替换，可以动态显示订单相关信息
4. 消息类型统一为 `order`，便于前端分类显示
5. 消息保留时间为90天（订单消息）
6. **权限控制**：用户只能查看自己的消息，通过JWT Token验证身份
7. **用户类型区分**：系统通过JWT中的`userType`字段区分客户、员工、管理员
8. **接口统一**：所有用户类型使用相同的消息接口，简化了系统架构

## 扩展效果

通过这次扩展，用户端现在可以在以下关键节点收到消息通知：
- ✅ 订单确认成功
- ✅ 员工接单
- ✅ 员工出发
- ✅ 服务开始
- ✅ 服务完成
- ✅ 订单取消
- ✅ 订单退款
- ✅ 服务时间修改

这大大提升了用户对订单状态的感知度，改善了用户体验。
